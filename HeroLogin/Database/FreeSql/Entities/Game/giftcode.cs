﻿using FreeSql.DatabaseModel;using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Threading.Tasks;
using Newtonsoft.Json;
using FreeSql.DataAnnotations;
using System.Net;
using Newtonsoft.Json.Linq;
using System.Net.NetworkInformation;
using NpgsqlTypes;
using Npgsql.LegacyPostgis;

namespace HeroYulgang.Database.FreeSql.Entities.Game {

	[JsonObject(MemberSerialization.OptIn), Table(DisableSyncStructure = true)]
	public partial class giftcode {

		[JsonProperty, Column(IsPrimary = true, IsIdentity = true, InsertValueSql = "nextval('giftcode_id_seq'::regclass)")]
		public int id { get; set; }

		[JsonProperty, Column(StringLength = -2)]
		public string username { get; set; }

		[JsonProperty, Column(StringLength = -2)]
		public string code { get; set; }

		[JsonProperty]
		public int? cash { get; set; }

		[JsonProperty]
		public int? bonus { get; set; }

		[JsonProperty, Column(StringLength = -2)]
		public string noi_dung { get; set; }

		[JsonProperty]
		public int? da_su_dung { get; set; }

		[JsonProperty, Column(StringLength = -2)]
		public string nguoi_su_dung { get; set; }

	}

}
