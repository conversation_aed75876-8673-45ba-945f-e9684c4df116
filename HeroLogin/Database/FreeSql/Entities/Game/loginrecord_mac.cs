﻿using FreeSql.DatabaseModel;using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Threading.Tasks;
using Newtonsoft.Json;
using FreeSql.DataAnnotations;
using System.Net;
using Newtonsoft.Json.Linq;
using System.Net.NetworkInformation;
using NpgsqlTypes;
using Npgsql.LegacyPostgis;

namespace HeroYulgang.Database.FreeSql.Entities.Game {

	[JsonObject(MemberSerialization.OptIn), Table(DisableSyncStructure = true)]
	public partial class loginrecord_mac {

		[JsonProperty, Column(IsPrimary = true, IsIdentity = true, InsertValueSql = "nextval('loginrecord_mac_id_seq'::regclass)")]
		public int id { get; set; }

		[JsonProperty, Column(StringLength = -2)]
		public string userid { get; set; }

		[JsonProperty, Column(StringLength = -2)]
		public string username { get; set; }

		[JsonProperty, Column(StringLength = -2)]
		public string userip { get; set; }

		[JsonProperty, Column(StringLength = -2)]
		public string loaihinh { get; set; }

		[JsonProperty]
		public DateTime? thoigian { get; set; }

		[JsonProperty, Column(StringLength = -2)]
		public string mac_address { get; set; }

		[JsonProperty]
		public int? serverid { get; set; }

	}

}
