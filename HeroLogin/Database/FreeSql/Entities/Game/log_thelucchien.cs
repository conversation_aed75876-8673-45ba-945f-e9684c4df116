﻿using FreeSql.DatabaseModel;using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Threading.Tasks;
using Newtonsoft.Json;
using FreeSql.DataAnnotations;
using System.Net;
using Newtonsoft.Json.Linq;
using System.Net.NetworkInformation;
using NpgsqlTypes;
using Npgsql.LegacyPostgis;

namespace HeroYulgang.Database.FreeSql.Entities.Game {

	[JsonObject(MemberSerialization.OptIn), Table(DisableSyncStructure = true)]
	public partial class log_thelucchien {

		[JsonProperty, Column(IsPrimary = true, IsIdentity = true, InsertValueSql = "nextval('log_thelucchien_id_seq'::regclass)")]
		public int id { get; set; }

		[JsonProperty, Column(StringLength = -2)]
		public string fld_name { get; set; }

		[JsonProperty]
		public int? giet { get; set; }

		[JsonProperty]
		public int? chet { get; set; }

		[JsonProperty, Column(DbType = "date")]
		public DateTime? ngay { get; set; }

		[JsonProperty, Column(StringLength = -2)]
		public string theluc { get; set; }

		[JsonProperty, Column(StringLength = -2)]
		public string monphai { get; set; }

	}

}
