﻿using Avalonia;
using System;
using System.IO;
using System.Text;
using System.Threading.Tasks;
using HeroYulgang.Views;
using HeroYulgang.Core;
using HeroYulgang.Services;
using RxjhServer;
using Akka.Hosting;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using HeroYulgang.Core.Actors;
using Akka.Actor;

namespace HeroYulgang;

class Program
{
    [STAThread]
    public static void Main(string[] args)
    {
        // Register CodePagesEncodingProvider to support Windows-1252 encoding
        Encoding.RegisterProvider(CodePagesEncodingProvider.Instance);

        // Check for GUI mode
        bool guiMode = Array.Exists(args, arg => arg.Equals("--gui", StringComparison.OrdinalIgnoreCase));
        bool autoStart = Array.Exists(args, arg => arg.Equals("--auto-start", StringComparison.OrdinalIgnoreCase));

        // Check for custom config path
        string configPath = null;
        string appSettingsPath = null;
        for (int i = 0; i < args.Length; i++)
        {
            if (args[i].StartsWith("--config=", StringComparison.OrdinalIgnoreCase))
            {
                configPath = args[i].Substring("--config=".Length);
            }
            else if (args[i].StartsWith("--appsettings=", StringComparison.OrdinalIgnoreCase))
            {
                appSettingsPath = args[i].Substring("--appsettings=".Length);
            }
        }

        if (!guiMode)
        {
            RunHeadlessDI().Wait();
        }
        else
        {
            BuildAvaloniaApp()
                .StartWithClassicDesktopLifetime(args);
        }
    }

    private static async Task RunHeadlessDI()
    {
                try
        {
            Logger.Instance.Info("Đang tự động khởi động World...");

            // Đợi một chút để UI được khởi tạo hoàn toàn
            await Task.Delay(2000);

            var world = World.Instance;

            // Khởi tạo heavy components trước
            await world.InitializeHeavyComponentsAsync();

            // Start World
            bool success = await world.StartAsync();

            if (success)
            {
                Logger.Instance.Info("✓ World đã được tự động khởi động thành công");
            }
            else
            {
                Logger.Instance.Error("✗ Không thể tự động khởi động World");
            }
        }
        catch (Exception ex)
        {
            Logger.Instance.Error($"Lỗi khi tự động khởi động World: {ex.Message}");
        }
    }

    private static async Task RunHeadlessConsoleNonBlocking(World world)
    {
        // Set console encoding to UTF-8 for Vietnamese characters
        try
        {
            Console.OutputEncoding = System.Text.Encoding.UTF8;
            Console.InputEncoding = System.Text.Encoding.UTF8;
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Warning: Could not set console encoding: {ex.Message}");
        }

        var cancellationTokenSource = new System.Threading.CancellationTokenSource();

        // Handle console input in background task - only if console is available
        Task consoleTask = null;

        try
        {
            // Check if console input is available (not redirected)
            if (!Console.IsInputRedirected && Environment.UserInteractive)
            {
                consoleTask = Task.Run(async () =>
                {
                    while (!cancellationTokenSource.Token.IsCancellationRequested)
                    {
                        try
                        {
                            // Use a safer approach without Console.KeyAvailable
                            var inputTask = Task.Run(() => Console.ReadLine());
                            var delayTask = Task.Delay(1000, cancellationTokenSource.Token);

                            var completedTask = await Task.WhenAny(inputTask, delayTask);

                            if (completedTask == inputTask && inputTask.Result != null)
                            {
                                var input = inputTask.Result.ToLower().Trim();
                                await HandleConsoleCommand(input, world, cancellationTokenSource);
                            }
                        }
                        catch (OperationCanceledException)
                        {
                            break;
                        }
                        catch (Exception ex)
                        {
                            Console.WriteLine($"Console input error: {ex.Message}");
                            await Task.Delay(5000, cancellationTokenSource.Token);
                        }
                    }
                }, cancellationTokenSource.Token);
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Console setup error: {ex.Message}");
        }

        // Keep the main thread alive
        try
        {
            if (consoleTask != null)
            {
                await consoleTask;
            }
            else
            {
                // If no console available, just wait indefinitely
                await Task.Delay(-1, cancellationTokenSource.Token);
            }
        }
        catch (OperationCanceledException)
        {
            // Expected when shutting down
        }
    }

    private static async Task HandleConsoleCommand(string input, World world, System.Threading.CancellationTokenSource cancellationTokenSource)
    {
        switch (input)
        {
            case "q":
            case "quit":
            case "exit":
                Console.WriteLine("Shutting down GameServer...");
                if (world.State == WorldState.Running)
                {
                    await world.StopAsync();
                }
                cancellationTokenSource.Cancel();
                Environment.Exit(0);
                break;

            case "s":
            case "status":
                ShowStatus(world);
                break;

            case "start":
                if (world.State != WorldState.Running)
                {
                    Console.WriteLine("Starting World...");
                    bool success = await world.StartAsync();
                    Console.WriteLine(success ? "✓ World started" : "✗ Failed to start World");
                }
                else
                {
                    Console.WriteLine("World is already running");
                }
                break;

            case "stop":
                if (world.State == WorldState.Running)
                {
                    Console.WriteLine("Stopping World...");
                    bool success = await world.StopAsync();
                    Console.WriteLine(success ? "✓ World stopped" : "✗ Failed to stop World");
                }
                else
                {
                    Console.WriteLine("World is not running");
                }
                break;

            case "restart":
                Console.WriteLine("Restarting World...");
                if (world.State == WorldState.Running)
                {
                    await world.StopAsync();
                    await Task.Delay(2000);
                }
                bool restartSuccess = await world.StartAsync();
                Console.WriteLine(restartSuccess ? "✓ World restarted" : "✗ Failed to restart World");
                break;

            case "help":
            case "h":
                ShowHelp();
                break;

            default:
                if (!string.IsNullOrEmpty(input))
                {
                    Console.WriteLine($"Unknown command: {input}. Type 'help' for available commands.");
                }
                break;
        }
    }

    private static async Task RunHeadlessConsole(World world)
    {
        while (true)
        {
            var input = Console.ReadLine()?.ToLower().Trim();

            switch (input)
            {
                case "q":
                case "quit":
                case "exit":
                    Console.WriteLine("Shutting down GameServer...");
                    if (world.State == WorldState.Running)
                    {
                        await world.StopAsync();
                    }
                    Environment.Exit(0);
                    break;

                case "s":
                case "status":
                    ShowStatus(world);
                    break;

                case "start":
                    if (world.State != WorldState.Running)
                    {
                        Console.WriteLine("Starting World...");
                        bool success = await world.StartAsync();
                        Console.WriteLine(success ? "✓ World started" : "✗ Failed to start World");
                    }
                    else
                    {
                        Console.WriteLine("World is already running");
                    }
                    break;

                case "stop":
                    if (world.State == WorldState.Running)
                    {
                        Console.WriteLine("Stopping World...");
                        bool success = await world.StopAsync();
                        Console.WriteLine(success ? "✓ World stopped" : "✗ Failed to stop World");
                    }
                    else
                    {
                        Console.WriteLine("World is not running");
                    }
                    break;

                case "restart":
                    Console.WriteLine("Restarting World...");
                    if (world.State == WorldState.Running)
                    {
                        await world.StopAsync();
                        await Task.Delay(2000);
                    }
                    bool restartSuccess = await world.StartAsync();
                    Console.WriteLine(restartSuccess ? "✓ World restarted" : "✗ Failed to restart World");
                    break;

                case "help":
                case "h":
                    ShowHelp();
                    break;

                default:
                    if (!string.IsNullOrEmpty(input))
                    {
                        Console.WriteLine($"Unknown command: {input}. Type 'help' for available commands.");
                    }
                    break;
            }
        }
    }

    private static void ShowStatus(World world)
    {
        Console.WriteLine("\n=== GameServer Status ===");
        Console.WriteLine($"State: {world.State}");
        Console.WriteLine($"Uptime: {(world.State == WorldState.Running ? DateTime.Now - world.StartTime : TimeSpan.Zero):hh\\:mm\\:ss}");
        Console.WriteLine($"Port: {ConfigManager.Instance.ServerSettings.GameServerPort}");
        Console.WriteLine($"Max Players: {ConfigManager.Instance.ServerSettings.MaximumOnline}");
        Console.WriteLine($"Server Name: {ConfigManager.Instance.ServerSettings.ServerName}");
        Console.WriteLine("========================\n");
    }

    private static void ShowHelp()
    {
        Console.WriteLine("\n=== Available Commands ===");
        Console.WriteLine("start    - Start the World");
        Console.WriteLine("stop     - Stop the World");
        Console.WriteLine("restart  - Restart the World");
        Console.WriteLine("status   - Show server status");
        Console.WriteLine("quit     - Shutdown server");
        Console.WriteLine("help     - Show this help");
        Console.WriteLine("=========================\n");
    }

    // Avalonia configuration, don't remove; also used by visual designer.
    public static AppBuilder BuildAvaloniaApp()
        => AppBuilder.Configure<App>()
            .UsePlatformDetect()
            .WithInterFont()
            .LogToTrace();
}
